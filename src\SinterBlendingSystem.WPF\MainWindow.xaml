<Window x:Class="SinterBlendingSystem.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="烧结配料系统 - SQP优化算法" 
        Height="800" Width="1200"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <!-- 顶部操作栏 -->
                <RowDefinition Height="*"/>
                <!-- 主内容区 -->
                <RowDefinition Height="Auto"/>
                <!-- 底部状态栏 -->
            </Grid.RowDefinitions>

            <!-- 顶部操作栏 -->
            <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" 
                    BorderBrush="{DynamicResource PrimaryHueDarkBrush}" BorderThickness="0,0,0,1">
                <Grid Height="60">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧：应用标题和图标 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <materialDesign:PackIcon Kind="Factory" Width="32" Height="32" 
                                               Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"/>
                        <TextBlock Text="烧结配料系统" FontSize="18" FontWeight="Medium" 
                                 Foreground="{DynamicResource PrimaryHueMidForegroundBrush}" 
                                 VerticalAlignment="Center" Margin="12,0,0,0"/>
                    </StackPanel>

                    <!-- 中间：主要操作按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Button x:Name="BtnOptimize" Style="{StaticResource MaterialDesignRaisedAccentButton}" 
                                Margin="8,0" Padding="16,8" Command="{Binding OptimizeCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="PlayCircle" Width="20" Height="20" Margin="0,0,8,0"/>
                                <TextBlock Text="开始优化"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="BtnSave" Style="{StaticResource MaterialDesignRaisedButton}" 
                                Margin="8,0" Padding="16,8" Command="{Binding SaveCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20" Margin="0,0,8,0"/>
                                <TextBlock Text="保存配置"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="BtnLoad" Style="{StaticResource MaterialDesignRaisedButton}" 
                                Margin="8,0" Padding="16,8" Command="{Binding LoadCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FolderOpen" Width="20" Height="20" Margin="0,0,8,0"/>
                                <TextBlock Text="加载配置"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- 右侧：系统状态和设置 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <!-- 服务状态指示器 -->
                        <Border Background="Green"
                                CornerRadius="8" Padding="8,4" Margin="0,0,8,0">
                            <TextBlock Text="{Binding ServiceStatusText}" FontSize="10"
                                     Foreground="White" FontWeight="Medium"/>
                        </Border>
                        
                        <!-- 设置按钮 -->
                        <Button Style="{StaticResource MaterialDesignIconButton}" 
                                Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                Command="{Binding SettingsCommand}">
                            <materialDesign:PackIcon Kind="Settings" Width="24" Height="24"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 主内容区 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200"/>
                    <!-- 左侧导航栏 -->
                    <ColumnDefinition Width="*"/>
                    <!-- 主内容区 -->
                </Grid.ColumnDefinitions>

                <!-- 左侧导航栏 -->
                <Border Grid.Column="0" Background="{DynamicResource MaterialDesignCardBackground}" 
                        BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,1,0">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="0,8">
                            <!-- 原料管理 -->
                            <Expander Header="原料管理" IsExpanded="True" Margin="8,4">
                                <StackPanel Margin="16,8,8,8">
                                    <Button x:Name="BtnMaterialList" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="原料列表" Command="{Binding NavigateCommand}" 
                                            CommandParameter="MaterialList"/>
                                    <Button x:Name="BtnMaterialEdit" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="原料编辑" Command="{Binding NavigateCommand}" 
                                            CommandParameter="MaterialEdit"/>
                                </StackPanel>
                            </Expander>

                            <!-- 优化配置 -->
                            <Expander Header="优化配置" IsExpanded="True" Margin="8,4">
                                <StackPanel Margin="16,8,8,8">
                                    <Button x:Name="BtnTargetConfig" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="目标配置" Command="{Binding NavigateCommand}" 
                                            CommandParameter="TargetConfig"/>
                                    <Button x:Name="BtnConstraintConfig" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="约束配置" Command="{Binding NavigateCommand}" 
                                            CommandParameter="ConstraintConfig"/>
                                </StackPanel>
                            </Expander>

                            <!-- 结果展示 -->
                            <Expander Header="结果展示" IsExpanded="True" Margin="8,4">
                                <StackPanel Margin="16,8,8,8">
                                    <Button x:Name="BtnResultComparison" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="方案对比" Command="{Binding NavigateCommand}" 
                                            CommandParameter="ResultComparison"/>
                                    <Button x:Name="BtnResultVisualization" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="结果可视化" Command="{Binding NavigateCommand}" 
                                            CommandParameter="ResultVisualization"/>
                                    <Button x:Name="BtnHistory" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="历史记录" Command="{Binding NavigateCommand}" 
                                            CommandParameter="History"/>
                                </StackPanel>
                            </Expander>

                            <!-- 系统工具 -->
                            <Expander Header="系统工具" Margin="8,4">
                                <StackPanel Margin="16,8,8,8">
                                    <Button x:Name="BtnImportExport" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="导入导出" Command="{Binding NavigateCommand}" 
                                            CommandParameter="ImportExport"/>
                                    <Button x:Name="BtnSystemSettings" Style="{StaticResource NavigationButtonStyle}" 
                                            Content="系统设置" Command="{Binding NavigateCommand}" 
                                            CommandParameter="SystemSettings"/>
                                </StackPanel>
                            </Expander>
                        </StackPanel>
                    </ScrollViewer>
                </Border>

                <!-- 主内容区 -->
                <Border Grid.Column="1" Background="{DynamicResource MaterialDesignPaper}">
                    <ContentControl x:Name="MainContentControl" Content="{Binding CurrentView}" 
                                  Margin="16"/>
                </Border>
            </Grid>

            <!-- 底部状态栏 -->
            <Border Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}" 
                    BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,1,0,0">
                <Grid Height="32">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧：状态信息 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <TextBlock Text="{Binding StatusMessage}" FontSize="11" 
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>

                    <!-- 中间：进度条（优化时显示） -->
                    <ProgressBar Grid.Column="1" Width="200" Height="4" Margin="8,0" 
                               Value="{Binding OptimizationProgress}" 
                               Visibility="{Binding IsOptimizing, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                    <!-- 右侧：系统信息 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <TextBlock Text="{Binding CurrentTime, StringFormat='yyyy-MM-dd HH:mm:ss'}" 
                                 FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </materialDesign:DialogHost>
</Window>

<!-- 替换第54行的BtnOptimize按钮 -->
<Button x:Name="BtnOptimize" BorderThickness="0" 
        Margin="8,0" Padding="16,8" Command="{Binding OptimizeCommand}"
        Cursor="Hand">
    <Button.Style>
        <Style TargetType="Button">
            <Setter Property="Background" Value="#42b983"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#38a169"/>
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Button.Style>
    <StackPanel Orientation="Horizontal">
        <materialDesign:PackIcon Kind="PlayCircle" Width="18" Height="18" Margin="0,0,8,0"/>
        <TextBlock Text="开始优化" FontWeight="Medium"/>
    </StackPanel>
</Button>

<!-- 替换第68行的BtnSave按钮 -->
<Button x:Name="BtnSave" Background="#42b983" Foreground="White" BorderThickness="0"
        Margin="8,0" Padding="16,8" Command="{Binding SaveCommand}" Cursor="Hand">
    <Button.Template>
        <ControlTemplate TargetType="Button">
            <Border Background="{TemplateBinding Background}" CornerRadius="6" Padding="{TemplateBinding Padding}">
                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Border>
        </ControlTemplate>
    </Button.Template>
    <StackPanel Orientation="Horizontal">
        <materialDesign:PackIcon Kind="ContentSave" Width="18" Height="18" Margin="0,0,8,0"/>
        <TextBlock Text="保存配置" FontWeight="Medium"/>
    </StackPanel>
</Button>

<!-- 替换第76行的BtnLoad按钮 -->
<Button x:Name="BtnLoad" Background="Transparent" Foreground="White" BorderBrush="#CFD9E1" BorderThickness="1"
        Margin="8,0" Padding="16,8" Command="{Binding LoadCommand}" Cursor="Hand">
    <Button.Template>
        <ControlTemplate TargetType="Button">
            <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" 
                    BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="6" Padding="{TemplateBinding Padding}">
                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Border>
        </ControlTemplate>
    </Button.Template>
    <StackPanel Orientation="Horizontal">
        <materialDesign:PackIcon Kind="FolderOpen" Width="18" Height="18" Margin="0,0,8,0"/>
        <TextBlock Text="加载配置" FontWeight="Medium"/>
    </StackPanel>
</Button>
