<UserControl x:Class="SinterBlendingSystem.WPF.Views.ResultVisualizationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <Grid>
        <Border Style="{StaticResource CardStyle}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="ChartPie" Width="64" Height="64" 
                                       Foreground="{StaticResource TextSecondaryBrush}" 
                                       HorizontalAlignment="Center" Margin="0,0,0,16"/>
                <TextBlock Text="结果可视化" Style="{StaticResource TitleTextStyle}" 
                           HorizontalAlignment="Center"/>
                <TextBlock Text="此功能将在后续版本实现" Style="{StaticResource BodyTextStyle}" 
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>

<!-- 替换第36行的按钮 -->
<Button Content="导出图表" Command="{Binding ExportChartsCommand}"
        Background="Transparent" Foreground="#2196F3" BorderBrush="#2196F3" BorderThickness="1"
        Padding="16,8" Margin="8,0">
    <Button.Template>
        <ControlTemplate TargetType="Button">
            <Border Background="{TemplateBinding Background}" 
                    BorderBrush="{TemplateBinding BorderBrush}" 
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Border>
        </ControlTemplate>
    </Button.Template>
</Button>

<Button Content="刷新数据" Command="{Binding RefreshDataCommand}"
        Background="#42b983" Foreground="White" BorderThickness="0"
        Padding="20,8" Margin="8,0">
    <Button.Template>
        <ControlTemplate TargetType="Button">
            <Border Background="{TemplateBinding Background}" CornerRadius="6" Padding="{TemplateBinding Padding}">
                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
            </Border>
        </ControlTemplate>
    </Button.Template>
</Button>
