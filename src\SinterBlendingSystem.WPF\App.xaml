<Application x:Class="SinterBlendingSystem.WPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="BlueGrey" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- 自定义样式 -->
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/CommonStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/DataGridStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/ButtonStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局字体设置 -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="FontFamily" Value="Microsoft YaHei UI" />
                <Setter Property="FontSize" Value="12" />
            </Style>

            <Style TargetType="{x:Type UserControl}">
                <Setter Property="FontFamily" Value="Microsoft YaHei UI" />
                <Setter Property="FontSize" Value="12" />
            </Style>

            <!-- 应用程序级别的转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!-- 自定义转换器 -->
            <local:StatusToColorConverter x:Key="StatusToColorConverter" xmlns:local="clr-namespace:SinterBlendingSystem.WPF.Converters"/>
            <local:StatusToBrushConverter x:Key="StatusToBrushConverter" xmlns:local="clr-namespace:SinterBlendingSystem.WPF.Converters"/>
            <local:EnumToBooleanConverter x:Key="EnumToBooleanConverter" xmlns:local="clr-namespace:SinterBlendingSystem.WPF.Converters"/>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>
