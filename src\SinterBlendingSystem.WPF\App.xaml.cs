using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Services;
using SinterBlendingSystem.WPF.ViewModels;
using System;
using System.Windows;

namespace SinterBlendingSystem.WPF
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            // 配置依赖注入容器
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // 注册服务
                    services.AddHttpClient();
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.AddDebug();
                        builder.SetMinimumLevel(LogLevel.Information);
                    });

                    // 注册核心服务
                    services.AddSingleton<IDataService, DataService>();
                    services.AddSingleton<IOptimizationService, OptimizationService>();

                    // 注册ViewModels
                    services.AddTransient<MainWindowViewModel>();
                    services.AddTransient<MaterialListViewModel>();
                    services.AddTransient<OptimizationConfigViewModel>();
                    services.AddTransient<ResultDisplayViewModel>();

                    // 注册主窗口
                    services.AddSingleton<MainWindow>();
                })
                .Build();

            // 启动主窗口
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            base.OnExit(e);
        }

        private void Application_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            var logger = _host?.Services.GetService<ILogger<App>>();
            logger?.LogError(e.Exception, "未处理的应用程序异常");

            MessageBox.Show($"应用程序发生未处理的异常：\n{e.Exception.Message}", 
                "错误", MessageBoxButton.OK, MessageBoxImage.Error);

            e.Handled = true;
        }
    }
}
