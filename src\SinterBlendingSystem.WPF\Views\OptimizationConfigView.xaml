<!-- 替换第118行的TextBox -->
<TextBox Text="{Binding Target.TFe, StringFormat=F2}"
         Background="White" BorderBrush="#E6E6E6" BorderThickness="1"
         Padding="8" FontSize="13" HorizontalContentAlignment="Right">
    <TextBox.Style>
        <Style TargetType="TextBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#42b983"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#42b983" Direction="270" ShadowDepth="0" BlurRadius="4" Opacity="0.3"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
    </TextBox.Style>
</TextBox>